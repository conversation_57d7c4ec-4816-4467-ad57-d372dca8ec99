#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取每个决策区的眼动注视平均深度脚本

功能：
1. 通过总标签文件获取所有第二次实验的实验数据
2. 按照决策区对每个文件计算Hit1注视点与受试者的平均距离
3. 累加每个文件的平均距离，最后算出每个决策区域的平均注视深度

作者:xcy
创建时间:2025-08-15
"""

import pandas as pd
import numpy as np
import os
from pathlib import Path
import math
from typing import Dict, List, Tuple, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DecisionEyeDepthExtractor:
    """决策区眼动注视深度提取器"""

    def __init__(self, data_root):
        """
        初始化提取器

        Args:
            data_root: 数据根目录路径
        """
        self.data_root = Path(data_root)
        self.labels_file = self.data_root / "all_file_labels.csv"

        # 决策区边界点定义 (X, Z坐标)
        self.decision_areas = {
            "Multi_In_Cabin": [[-40, 0.801], [-45, 0.801], [-45, -1.345], [-40, -1.345]],
            "Single_After_Cabin": [[-40, -1.869], [-45, -1.869], [-45, -3.238], [-40, -3.238]],
            "Continuous": [[6.51, -1.53], [-36.66, -1.53], [-36.66, -3.328], [6.51, -3.328]],
            "Single_U_Turn_Low_Fog": [[39.088, -1.3], [17.99, -1.3], [17.99, -3], [39.088, -3]],
            "Single_U_Turn_Mid_Fog": [[39.088, -1.3], [33.472, -1.3], [33.472, -3], [39.088, -3]],
            "Single_U_Turn_High_Fog": [[47.62, 1.615], [41.866, 1.615], [41.866, -1.3], [47.62, -1.3]]
        }

        # 存储结果
        self.results = {}

    def load_second_experiment_files(self) -> List[str]:
        """
        从总标签文件中获取所有第二次实验的organized_clean_merged_Subject_Simulation_Info文件路径

        Returns:
            第二次实验的文件路径列表
        """
        try:
            # 读取标签文件
            df_labels = pd.read_csv(self.labels_file, encoding='utf-8-sig', engine='python')

            # 筛选第二次实验且为Subject_Simulation_Info类型的文件
            second_exp_files = df_labels[
                (df_labels['experiment'] == 2) &
                (df_labels['data_type'] == 1)]

            # 获取对应的organized_clean_merged文件路径
            file_paths = []
            for _, row in second_exp_files.iterrows():
                file_path = Path(row['file_path'])
                # 将Subject_Simulation_Info替换为organized_clean_merged_Subject_Simulation_Info
                organized_file_path = file_path.parent / f"organized_clean_merged_{file_path.stem}.csv"

                # 检查文件是否存在
                if os.path.exists(organized_file_path):
                    file_paths.append(organized_file_path)
                else:
                    logger.warning(f"文件不存在: {organized_file_path}")

            logger.info(f"找到 {len(file_paths)} 个第二次实验的数据文件")
            return file_paths

        except Exception as e:
            logger.error(f"加载第二次实验文件列表失败: {e}")
            return []

    def point_in_polygon(self, point: Tuple[float, float], polygon: List[List[float]]) -> bool:
        """
        判断点是否在多边形内（使用射线法）

        Args:
            point: 点坐标 (x, z)
            polygon: 多边形顶点列表 [[x1, z1], [x2, z2], ...]

        Returns:
            True如果点在多边形内,否则False
        """
        x, z = point
        n = len(polygon)
        inside = False

        p1x, p1z = polygon[0]
        for i in range(1, n + 1):
            p2x, p2z = polygon[i % n]
            if z > min(p1z, p2z):
                if z <= max(p1z, p2z):
                    if x <= max(p1x, p2x):
                        if p1z != p2z:
                            xinters = (z - p1z) * (p2x - p1x) / (p2z - p1z) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1z = p2x, p2z

        return inside
    
    def point_in_area(self, point: Tuple[float, float], polygon: List[List[float]]) -> bool:
        """
        判断点是否在决策区域内
        Args:
            pos_x: X坐标
            pos_z: Z坐标

        Returns:
            决策区域名称,如果不在任何决策区则返回None
        """

        x, z = point
        n = len(polygon)

        # 提取多边形的最小和最大坐标
        min_x, min_z = 0
        max_x, max_z = 0

        for i in range(n):
            min_x = min(min_x, polygon[i][0])
            min_z = min(min_z, polygon[i][1])
            max_x = max(max_x, polygon[i][0])
            max_z = max(max_z, polygon[i][1])

        if not (min_x <= x <= max_x and min_z <= z <= max_z):
            return True

        return False

    def get_decision_area(self, pos_x: float, pos_z: float) -> Optional[str]:
        """
        根据受试者位置判断所在的决策区域

        Args:
            pos_x: X坐标
            pos_z: Z坐标

        Returns:
            决策区域名称,如果不在任何决策区则返回None
        """
        point = (pos_x, pos_z)

        for area_name, polygon in self.decision_areas.items():
            if self.point_in_area(point, polygon):
                return area_name

        return None

    def calculate_distance(self, pos1: Tuple[float, float, float],
                          pos2: Tuple[float, float, float]) -> float:
        """
        计算两点之间的3D欧几里得距离

        Args:
            pos1: 第一个点的坐标 (x, y, z)
            pos2: 第二个点的坐标 (x, y, z)

        Returns:
            两点之间的距离
        """
        return math.sqrt(
            (pos1[0] - pos2[0]) ** 2 +
            (pos1[1] - pos2[1]) ** 2 +
            (pos1[2] - pos2[2]) ** 2
        )

    def process_file(self, file_path: str) -> Dict[str, List[float]]:
        """
        处理单个数据文件，计算每个决策区域的注视距离

        Args:
            file_path: 数据文件路径

        Returns:
            每个决策区域的距离列表字典
        """
        try:
            # 读取数据文件
            df = pd.read_csv(file_path, encoding='utf-8-sig')

            # 检查必要的列是否存在
            required_cols = ['PosX', 'PosY', 'PosZ', 'Hit1_X', 'Hit1_Y', 'Hit1_Z']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                logger.warning(f"文件 {file_path} 缺少列: {missing_cols}")
                return {}

            # 过滤掉Hit1坐标为0的行（表示没有注视点）
            df_valid = df[
                (df['Hit1_X'] != 0) | (df['Hit1_Y'] != 0) | (df['Hit1_Z'] != 0)
            ].copy()

            if df_valid.empty:
                logger.warning(f"文件 {file_path} 没有有效的Hit1数据")
                return {}

            # 按决策区域分组计算距离
            area_distances = {}

            for _, row in df_valid.iterrows():
                # 受试者位置
                subject_pos = (row['PosX'], row['PosY'], row['PosZ'])

                # Hit1注视点位置
                hit1_pos = (row['Hit1_X'], row['Hit1_Y'], row['Hit1_Z'])

                # 判断受试者所在的决策区域
                area = self.get_decision_area(row['PosX'], row['PosZ'])

                if area:
                    # 计算距离
                    distance = self.calculate_distance(subject_pos, hit1_pos)

                    if area not in area_distances:
                        area_distances[area] = []
                    area_distances[area].append(distance)

            logger.info(f"处理文件 {os.path.basename(file_path)}: 找到 {len(area_distances)} 个决策区域的数据")
            return area_distances

        except Exception as e:
            logger.error(f"处理文件 {file_path} 失败: {e}")
            return {}

    def extract_decision_eye_depth(self) -> Dict[str, float]:
        """
        提取所有决策区域的平均眼动注视深度

        Returns:
            每个决策区域的平均注视深度字典
        """
        # 获取第二次实验的文件列表
        file_paths = self.load_second_experiment_files()

        if not file_paths:
            logger.error("没有找到第二次实验的数据文件")
            return {}

        # 累积所有文件的距离数据
        all_area_distances = {}

        for file_path in file_paths:
            logger.info(f"正在处理文件: {file_path.name}")

            file_distances = self.process_file(file_path)

            # 累积到总的距离数据中
            for area, distances in file_distances.items():
                if area not in all_area_distances:
                    all_area_distances[area] = []
                all_area_distances[area].extend(distances)

        # 计算每个决策区域的平均深度
        average_depths = {}
        for area, distances in all_area_distances.items():
            if distances:
                average_depth = np.mean(distances)
                average_depths[area] = average_depth
                logger.info(f"决策区域 {area}: 平均注视深度 = {average_depth:.4f} (样本数: {len(distances)})")
            else:
                logger.warning(f"决策区域 {area}: 没有有效数据")

        return average_depths

    def save_results(self, results: Dict[str, float], output_file: str = "decision_eye_depth_results.csv"):
        """
        保存结果到CSV文件

        Args:
            results: 结果字典
            output_file: 输出文件名
        """
        try:
            # 创建结果DataFrame
            df_results = pd.DataFrame([
                {"Decision_Area": area, "Average_Eye_Depth": depth}
                for area, depth in results.items()
            ])

            # 保存到CSV文件
            output_path = self.data_root / output_file
            df_results.to_csv(output_path, index=False, encoding='utf-8-sig')

            logger.info(f"结果已保存到: {output_path}")

            # 打印结果摘要
            print("\n=== 决策区域眼动注视平均深度结果 ===")
            for area, depth in results.items():
                print(f"{area}: {depth:.4f}")

        except Exception as e:
            logger.error(f"保存结果失败: {e}")

def get_grand_parent_folder():
    import os
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder

def get_target_file_path():
    import os
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

def main():
    """主函数"""
    try:
        # 创建提取器实例
        extractor = DecisionEyeDepthExtractor(get_target_file_path())

        # 提取决策区域眼动注视深度
        logger.info("开始提取决策区域眼动注视深度...")
        results = extractor.extract_decision_eye_depth()

        if results:
            # 保存结果
            extractor.save_results(results)
            logger.info("提取完成！")
        else:
            logger.error("没有提取到任何结果")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")

if __name__ == "__main__":
    main()