import os
import csv
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from pathlib import Path
from datetime import datetime
import math
from scipy import signal
from scipy.spatial.distance import pdist, squareform

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class EyeIndicatorAssessment:
    """眼动指标评估器"""

    def __init__(self, data_root):
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "analysis_results" / "eye_gaze" / "indicators"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"眼动指标评估文件路径为：{self.output_dir}")
        
        # 分析参数
        self.min_fixation_duration = 0.1  # 最短注视时间（秒）
        
        # 扫视检测参数
        self.saccade_min_velocity = 5.0  # 最小扫视速度 (°/s)
        self.saccade_min_duration = 0.02  # 最小扫视持续时间 (20ms)
        self.saccade_min_amplitude = 0.5   # 最小扫视幅度 (°)
        self.saccade_noise_multiplier = 5.0  # 噪声标准差倍数
        self.saccade_window_size = 2  # 跨帧差分窗口大小 (k=2)
        self.filter_cutoff = 20.0  # Butterworth低通滤波截止频率 (Hz)
        self.filter_order = 4  # 滤波器阶数
        
        # 镜像参考点
        self.mirror_point = (-92.5, -0.4318, -0.4991)
        
        # 定义决策区边界（复制自EvacuationPathAnalysis.py）
        self.decision_zones = {
            'zone1': {
                'bound': [(-45.0, -40.0), (-1.345, 0.801)],
                'title': 'DP1'
            },
            'zone2': {
                'bound': [(-45.0, -40.0), (-3.238, -1.869)],
                'title': 'DP2'
            },
            'zone3': {
                'bound': [(-36.66, 6.51), (-3.328, -1.53)],
                'title': 'DP3'
            }
        }
        
        # 存储结果
        self.individual_results = []  # 每次实验的结果
        self.group_zone_results = []  # 分组+决策区的结果

    @staticmethod
    def transform_to_local_coordinate(world_point, subject_position, head_rotation):
        """
        将世界坐标转换为相对于受试者的局部坐标
        world_point: 世界坐标中的点 (np.array)
        subject_position: 受试者位置 (np.array)
        head_rotation: 受试者头部旋转（欧拉角，度）- Unity中为ZXY顺序(Roll→Pitch→Yaw)
        """
        relative_position = world_point - subject_position
        head_rotation_rad = np.radians(head_rotation)
        roll = head_rotation_rad[2]   # Z轴旋转 (Roll)
        pitch = head_rotation_rad[0]  # X轴旋转 (Pitch)
        yaw = head_rotation_rad[1]    # Y轴旋转 (Yaw)
        rot_z = np.array([
            [np.cos(roll), -np.sin(roll), 0],
            [np.sin(roll), np.cos(roll), 0],
            [0, 0, 1]
        ])
        rot_x = np.array([
            [1, 0, 0],
            [0, np.cos(pitch), -np.sin(pitch)],
            [0, np.sin(pitch), np.cos(pitch)]
        ])
        rot_y = np.array([
            [np.cos(yaw), 0, np.sin(yaw)],
            [0, 1, 0],
            [-np.sin(yaw), 0, np.cos(yaw)]
        ])
        rotation_matrix = rot_y @ rot_x @ rot_z
        local_position = np.linalg.inv(rotation_matrix) @ relative_position
        return local_position

    def analyze_all_eye_indicators(self):
        """分析所有眼动指标"""
        print("###开始分析眼动指标###")
        
        # 读取总标签文件
        label_file = self.data_root / "all_file_labels.csv"
        if not label_file.exists():
            print("未找到总标签文件, 请先运行标签处理脚本")
            return
        
        labels_df = pd.read_csv(label_file, encoding='utf-8-sig', engine='python')
        
        # 筛选第二部分实验的眼动数据
        second_eyegaze_files = labels_df[(labels_df['data_type'] == 1) & (labels_df['experiment'] == 2)].copy()
        
        if len(second_eyegaze_files) == 0:
            print("未找到第二部分眼动数据")
            return
        
        print(f"找到{len(second_eyegaze_files)}个第二部分眼动数据文件")
        
        # 按烟气浓度和NPC数量分组
        smoke_npc_combinations = [
            (1, 0), (1, 10), (1, 20),   # 低浓度
            (2, 0), (2, 10), (2, 20),   # 中浓度  
            (3, 0), (3, 10), (3, 20)    # 高浓度
        ]
        
        # 存储分组统计
        group_stats = {}
        
        for smoke_level, npc_count in smoke_npc_combinations:
            # 筛选特定烟气浓度和NPC数量的数据
            condition_data = second_eyegaze_files[
                (second_eyegaze_files['smoke_level'] == smoke_level) & 
                (second_eyegaze_files['npc_count'] == npc_count)
            ].copy()
            
            if len(condition_data) == 0:
                print(f"烟气等级{smoke_level}+NPC{npc_count}的数据为空，跳过")
                continue
            
            print(f"开始分析烟气等级{smoke_level}+NPC{npc_count}的数据，共{len(condition_data)}个文件")
            
            # 存储该组的所有结果
            group_results = []
            
            # 分析每个文件
            for _, row in condition_data.iterrows():
                result = self.analyze_single_eye_data(row)
                if result:
                    self.individual_results.append(result)
                    group_results.append(result)
            
            # 计算该组的决策区统计
            if group_results:
                group_zone_stats = self.calculate_group_zone_statistics(group_results, smoke_level, npc_count)
                self.group_zone_results.append(group_zone_stats)
        
        # 保存结果
        self.save_results()
        
        print("###眼动指标分析完成###")

    def analyze_single_eye_data(self, file_info):
        """分析单个眼动数据文件"""
        # 构建文件路径
        file_path = Path(file_info['file_path'])
        organized_file_path = file_path.parent / f"organized_clean_merged_{file_path.stem}.csv"
        
        if not organized_file_path.exists():
            print(f"文件不存在: {organized_file_path}")
            return None
        
        print(f"分析文件: {organized_file_path.name}")
        
        try:
            # 读取数据
            df = pd.read_csv(organized_file_path, encoding='utf-8-sig', engine='python')
            
            # 处理时间戳
            df['TimeStamp'] = pd.to_datetime(df['TimeStamp'], format='%Y:%m:%H:%M:%S:%f', errors='coerce')
            df = df.dropna(subset=['TimeStamp']).sort_values('TimeStamp').reset_index(drop=True)
            
            if len(df) == 0:
                print(f"文件{organized_file_path.name}没有有效数据")
                return None
            
            # 坐标转换：将Hit1/2/3转换为相对坐标
            for hit_idx in [1, 2, 3]:
                rel_xs, rel_ys, rel_zs = [], [], []
                for i, row in df.iterrows():
                    # 受试者位置和头部旋转
                    if not all(col in df.columns for col in [f'PosX', f'PosY', f'PosZ', f'HeadRotX', f'HeadRotY', f'HeadRotZ']):
                        rel_xs.append(np.nan)
                        rel_ys.append(np.nan)
                        rel_zs.append(np.nan)
                        continue
                    subject_pos = np.array([row['PosX'], row['PosY'], row['PosZ']])
                    head_rot = np.array([row['HeadRotX'], row['HeadRotY'], row['HeadRotZ']])
                    hit_cols = [f'Hit{hit_idx}_X', f'Hit{hit_idx}_Y', f'Hit{hit_idx}_Z']
                    if not all(col in df.columns for col in hit_cols):
                        rel_xs.append(np.nan)
                        rel_ys.append(np.nan)
                        rel_zs.append(np.nan)
                        continue
                    hit_point = np.array([row[hit_cols[0]], row[hit_cols[1]], row[hit_cols[2]]])
                    if np.any(pd.isna(hit_point)):
                        rel_xs.append(np.nan)
                        rel_ys.append(np.nan)
                        rel_zs.append(np.nan)
                        continue
                    rel = self.transform_to_local_coordinate(hit_point, subject_pos, head_rot)
                    rel_xs.append(rel[0])
                    rel_ys.append(rel[1])
                    rel_zs.append(rel[2])
                df[f'Hit{hit_idx}_X'] = rel_xs
                df[f'Hit{hit_idx}_Y'] = rel_ys
                df[f'Hit{hit_idx}_Z'] = rel_zs
            
            # 1. 计算注视时间
            fixation_duration = self.calculate_fixation_duration(df, file_info)
            
            # 2. 计算扫视速度和距离（用相对坐标）
            scan_metrics = self.calculate_scan_metrics(df)
            
            # 3. 计算决策区内的指标（用相对坐标）
            zone_metrics = self.calculate_zone_metrics(df, file_info)
            
            return {
                'file_path': str(organized_file_path),
                'subject_id': file_info['subject_id'],
                'experiment': file_info['experiment'],
                'smoke_level': file_info['smoke_level'],
                'npc_count': file_info['npc_count'],
                'is_mirror': file_info['is_mirror'],
                'fixation_duration': fixation_duration,
                'scan_speed': scan_metrics['speed'],
                'scan_distance_total': scan_metrics['distance_total'],
                'scan_distance_x': scan_metrics['distance_x'],
                'scan_distance_y': scan_metrics['distance_y'],
                'saccade_count': scan_metrics['saccade_count'],
                'mean_peak_velocity': scan_metrics['mean_peak_velocity'],
                'median_amplitude': scan_metrics['median_amplitude'],
                'zone_metrics': zone_metrics
            }
            
        except Exception as e:
            print(f"分析文件{organized_file_path.name}时出错: {e}")
            return None

    def calculate_fixation_duration(self, df, file_info):
        """计算注视时间 - 参考EyeGazeDataAnalysis.py的analyze_gaze_point_types方法"""
        print("计算注视时间...")
        
        # 分类统计
        type_counts = {
            '疏散标识': 0,
            '火': 0,
            'Npc': 0,
            '其他物体': 0
        }
        
        # 对Hit1, Hit2, Hit3分别处理
        for hit_col in ['Hit1_Name', 'Hit2_Name', 'Hit3_Name']:
            if hit_col not in df.columns:
                continue
                
            # 检测连续相同名称的序列
            fixation_sequences = self.detect_fixation_sequences(df, hit_col)
            
            # 统计各类型
            for seq in fixation_sequences:
                hit_name = seq['name']
                duration = seq['duration']
                
                # 只统计持续时间超过阈值的注视点
                if duration >= self.min_fixation_duration:
                    if '疏散标识' in hit_name:
                        type_counts['疏散标识'] += 1
                    elif 'Fire' in hit_name and any(c.isdigit() for c in hit_name):
                        type_counts['火'] += 1
                    elif hit_name.isdigit() and file_info['npc_count'] > 0:
                        type_counts['Npc'] += 1
                    elif hit_name != 'Null' and hit_name != 'nan':
                        type_counts['其他物体'] += 1
        
        # 计算总注视时间（秒）
        total_fixation_time = sum(type_counts.values()) * self.min_fixation_duration
        
        return {
            'total_time': total_fixation_time,
            'evacuation_sign': type_counts['疏散标识'] * self.min_fixation_duration,
            'fire': type_counts['火'] * self.min_fixation_duration,
            'npc': type_counts['Npc'] * self.min_fixation_duration,
            'other': type_counts['其他物体'] * self.min_fixation_duration
        }

    def detect_fixation_sequences(self, df, hit_name_col):
        """检测连续相同名称的注视序列 - 参考EyeGazeDataAnalysis.py"""
        sequences = []
        
        if len(df) == 0:
            return sequences
        
        current_name = None
        start_idx = 0
        
        for i in range(len(df)):
            name = str(df.iloc[i][hit_name_col])
            
            # 跳过无效值
            if name in ['Null', 'nan', '']:
                if current_name is not None:
                    # 结束当前序列
                    end_idx = i - 1
                    if end_idx > start_idx:  # 至少有2个点才能计算持续时间
                        duration = (df.iloc[end_idx]['TimeStamp'] - df.iloc[start_idx]['TimeStamp']).total_seconds()
                        sequences.append({
                            'name': current_name,
                            'start_idx': start_idx,
                            'end_idx': end_idx,
                            'duration': duration
                        })
                    current_name = None
                continue
            
            if current_name != name:
                # 结束上一个序列
                if current_name is not None:
                    end_idx = i - 1
                    if end_idx > start_idx:
                        duration = (df.iloc[end_idx]['TimeStamp'] - df.iloc[start_idx]['TimeStamp']).total_seconds()
                        sequences.append({
                            'name': current_name,
                            'start_idx': start_idx,
                            'end_idx': end_idx,
                            'duration': duration
                        })
                
                # 开始新序列
                current_name = name
                start_idx = i
        
        # 处理最后一个序列
        if current_name is not None:
            end_idx = len(df) - 1
            if end_idx > start_idx:
                duration = (df.iloc[end_idx]['TimeStamp'] - df.iloc[start_idx]['TimeStamp']).total_seconds()
                sequences.append({
                    'name': current_name,
                    'start_idx': start_idx,
                    'end_idx': end_idx,
                    'duration': duration
                })
        
        return sequences

    def detect_saccades(self, df):
        """
        检测扫视事件 - 基于生理规律的改进方法
        使用Butterworth低通滤波和跨帧差分法
        """
        print("检测扫视事件...")
        
        if len(df) < 3:
            return []
        
        # 提取Hit1坐标数据
        hit_cols = ['Hit1_X', 'Hit1_Y', 'Hit1_Z']
        if not all(col in df.columns for col in hit_cols):
            return []
        
        # 获取有效数据点
        valid_mask = df[hit_cols].notna().all(axis=1)
        if valid_mask.sum() < 3:
            return []
        
        valid_df = df[valid_mask].copy()
        positions = valid_df[hit_cols].values
        
        # 1. Butterworth低通滤波 (20Hz, 4阶)
        sample_rate = 45.0  # 45Hz采样率
        nyquist = sample_rate / 2.0
        normalized_cutoff = self.filter_cutoff / nyquist
        
        if normalized_cutoff >= 1.0:
            print("警告：截止频率过高，跳过滤波")
            filtered_positions = positions
        else:
            try:
                # 设计Butterworth低通滤波器
                b, a = signal.butter(self.filter_order, normalized_cutoff, btype='low')
                # 应用滤波器到每个坐标轴
                filtered_positions = np.zeros_like(positions)
                for i in range(positions.shape[1]):
                    filtered_positions[:, i] = signal.filtfilt(b, a, positions[:, i])
            except Exception as e:
                print(f"滤波失败: {e}，使用原始数据")
                filtered_positions = positions
        
        print("计算差分速度")
        # 2. 计算跨帧差分速度
        dt = 1.0 / sample_rate  # 时间间隔
        k = self.saccade_window_size  # 跨帧窗口大小
        
        velocities = []
        for i in range(k, len(filtered_positions) - k):
            # 计算跨帧位移
            pos_forward = filtered_positions[i + k]
            pos_backward = filtered_positions[i - k]
            displacement = pos_forward - pos_backward

            # 前面已经转换为了相对坐标，所以受试者位置为原点
            pos = (0, 0, 0)

            # 计算注视点间距
            distance_eye = np.linalg.norm(displacement)

            # 计算受试者视线距离
            distance = (np.linalg.norm(pos - pos_forward) + np.linalg.norm(pos - pos_backward)) / 2

            # 计算角度值
            radian = np.arccos((2 * (distance ** 2) - (distance_eye ** 2)) / (2 * (distance ** 2)))

            # 弧度转角度
            deg = np.degrees(radian)
            
            # 转换为角度速度 (°/s)
            velocity_deg_s = deg / (2 * k * dt)
            velocities.append(velocity_deg_s)
        
        if len(velocities) == 0:
            return []
        
        velocities = np.array(velocities)
        
        print("计算基线噪声")
        # 3. 计算基线噪声标准差
        #baseline_noise = np.std(velocities[:100]) # 截取前5s计算基线噪声标准差
        baseline_noise = np.median(np.abs(velocities - np.nanmedian(velocities))) * 1.4826
        print(f"基线标准差为:{baseline_noise}")
        velocity_threshold = max(self.saccade_min_velocity, 
                               self.saccade_noise_multiplier * baseline_noise)
        
        print("检测扫视事件")
        # 4. 检测扫视事件
        saccade_events = []
        in_saccade = False
        saccade_start = 0
        saccade_peak_velocity = 0
        accumulate_vel = 0 # 累积速度
        sub_pos = (0, 0, 0)
        
        for i, vel in enumerate(velocities):
            if vel > velocity_threshold:
                accumulate_vel += vel
                if not in_saccade:
                    # 开始扫视
                    in_saccade = True
                    saccade_start = i
                    saccade_peak_velocity = vel
                else:
                    # 更新峰值速度
                    saccade_peak_velocity = max(saccade_peak_velocity, vel)
            else:
                if in_saccade:
                    # 结束扫视
                    saccade_end = i
                    duration = (saccade_end - saccade_start + 1) * dt
                    
                    # 检查持续时间
                    if duration > self.saccade_min_duration:
                        # 计算扫视幅度
                        start_pos = filtered_positions[saccade_start + k]
                        end_pos = filtered_positions[saccade_end + k]
                        #amplitude = accumulate_vel * (2 * self.saccade_window_size * (1 / sample_rate))  # 转换为角度
                        d_eye = np.linalg.norm(start_pos - end_pos)
                        d_sub = (np.linalg.norm(start_pos - sub_pos) + 
                                 np.linalg.norm(end_pos - sub_pos)) / 2
                        amplitude = np.degrees(np.arccos((2 * (d_sub ** 2) - (d_eye ** 2)) / (2 * (d_sub ** 2))))


                        accumulate_vel = 0
                        # 检查幅度
                        if amplitude >= self.saccade_min_amplitude:
                            saccade_events.append({
                                'start_idx': saccade_start + k,
                                'end_idx': saccade_end + k,
                                'duration': duration,
                                'peak_velocity': saccade_peak_velocity,
                                'amplitude': amplitude,
                                'start_position': start_pos,
                                'end_position': end_pos
                            })
                    
                    in_saccade = False
        
        # 处理最后一个扫视事件
        if in_saccade:
            saccade_end = len(velocities) - 1
            duration = (saccade_end - saccade_start + 1) * dt
            
            if duration > self.saccade_min_duration:
                start_pos = filtered_positions[saccade_start + k]
                end_pos = filtered_positions[saccade_end + k]
                d_eye = np.linalg.norm(start_pos - end_pos)
                d_sub = (np.linalg.norm(start_pos - sub_pos) + 
                                 np.linalg.norm(end_pos - sub_pos)) / 2
                amplitude = np.degrees(np.arccos((2 * (d_sub ** 2) - (d_eye ** 2)) / (2 * (d_sub ** 2))))

                
                if amplitude >= self.saccade_min_amplitude:
                    saccade_events.append({
                        'start_idx': saccade_start + k,
                        'end_idx': saccade_end + k,
                        'duration': duration,
                        'peak_velocity': saccade_peak_velocity,
                        'amplitude': amplitude,
                        'start_position': start_pos,
                        'end_position': end_pos
                    })
        
        print(f"检测到 {len(saccade_events)} 个扫视事件")
        return saccade_events

    def calculate_saccade_metrics(self, saccade_events):
        """
        计算扫视指标统计量
        """
        if not saccade_events:
            return {
                'saccade_count': 0,
                'mean_peak_velocity': 0.0,
                'median_amplitude': 0.0,
                'mean_duration': 0.0,
                'total_distance': 0.0,
                'distance_x': 0.0,
                'distance_y': 0.0
            }
        
        # 提取指标
        peak_velocities = [event['peak_velocity'] for event in saccade_events]
        amplitudes = [event['amplitude'] for event in saccade_events]
        durations = [event['duration'] for event in saccade_events]
        
        # 计算总距离（球面角距离）
        total_distance = sum(event['amplitude'] for event in saccade_events)
        
        # 计算X和Y方向的距离
        x_distances = []
        y_distances = []
        for event in saccade_events:
            start_pos = event['start_position']
            end_pos = event['end_position']
            dx = abs(end_pos[0] - start_pos[0]) * 0.1  # 转换为角度
            dy = abs(end_pos[1] - start_pos[1]) * 0.1
            x_distances.append(dx)
            y_distances.append(dy)
        
        return {
            'saccade_count': len(saccade_events),
            'mean_peak_velocity': round(np.mean(peak_velocities), 3),
            'median_amplitude': round(np.median(amplitudes), 3),
            'mean_duration': round(np.mean(durations), 3),
            'total_distance': round(total_distance, 3),
            'distance_x': round(np.sum(x_distances), 3),
            'distance_y': round(np.sum(y_distances), 3)
        }

    def calculate_scan_metrics(self, df):
        """计算扫视速度和距离 - 基于生理规律的改进方法"""
        print("计算扫视指标...")
        
        # 检测扫视事件
        saccade_events = self.detect_saccades(df)
        
        if not saccade_events:
            print("未检测到有效扫视事件")
            return {
                'speed': 0.0, 
                'distance_total': 0.0,
                'distance_x': 0.0,
                'distance_y': 0.0,
                'saccade_count': 0,
                'mean_peak_velocity': 0.0,
                'median_amplitude': 0.0
            }
        
        # 计算扫视指标
        saccade_metrics = self.calculate_saccade_metrics(saccade_events)
        
        # 验证生理合理性
        if saccade_metrics['mean_peak_velocity'] < 80 or saccade_metrics['mean_peak_velocity'] > 300:
            print(f"警告：平均峰值速度 {saccade_metrics['mean_peak_velocity']}°/s 超出正常范围 (80-300°/s)")
        
        if saccade_metrics['median_amplitude'] < 5 or saccade_metrics['median_amplitude'] > 20:
            print(f"警告：中位数幅度 {saccade_metrics['median_amplitude']}° 超出正常范围 (5-20°)")
        
        return {
            'speed': saccade_metrics['total_distance'] / (saccade_metrics['mean_duration'] * len(saccade_events)) if saccade_metrics['mean_duration'] > 0 else 0.0,
            'distance_total': saccade_metrics['total_distance'],
            'distance_x': saccade_metrics['distance_x'],
            'distance_y': saccade_metrics['distance_y'],
            'saccade_count': saccade_metrics['saccade_count'],
            'mean_peak_velocity': saccade_metrics['mean_peak_velocity'],
            'median_amplitude': saccade_metrics['median_amplitude']
        }

    def calculate_zone_metrics(self, df, file_info):
        """计算决策区内的指标"""
        print("计算决策区指标...")
        
        # 获取决策区4的边界（根据烟气等级）
        zone4_bounds = self.get_decision_zone4_bounds(file_info['smoke_level'])
        decision_zones = self.decision_zones.copy()
        decision_zones['zone4'] = {
            'bound': zone4_bounds,
            'title': 'DP4'
        }
        
        zone_metrics = {}
        
        # 对每个决策区计算指标
        for zone_name, zone_info in decision_zones.items():
            zone_metrics[zone_name] = self.calculate_single_zone_metrics(df, zone_info, file_info)
        
        return zone_metrics

    def get_decision_zone4_bounds(self, smoke_level):
        """根据烟气等级获取决策区4的边界 - 复制自EvacuationPathAnalysis.py"""
        if smoke_level == 1:
            return [(17.99, 39.088), (-3, -1.3)]
        elif smoke_level == 2:
            return [(33.472, 39.088), (-3, -1.3)]
        else:
            return [(41.866, 47.62), (-1.3, 1.615)]

    def calculate_single_zone_metrics(self, df, zone_info, file_info):
        """计算单个决策区的指标"""
        x_bounds, z_bounds = zone_info['bound']
        
        # 筛选在决策区内的数据点
        zone_mask = (
            (df['PosX'] >= x_bounds[0]) & (df['PosX'] <= x_bounds[1]) &
            (df['PosZ'] >= z_bounds[0]) & (df['PosZ'] <= z_bounds[1])
        )
        
        zone_df = df[zone_mask].copy()
        
        if len(zone_df) == 0:
            return {
                'fixation_duration': 0.0,
                'evacuation_sign': 0.0,
                'fire': 0.0,
                'npc': 0.0,
                'scan_speed': 0.0,
                'scan_distance': 0.0,
                'saccade_count': 0.0,
                'mean_peak_velocity': 0.0,
                'median_amplitude': 0.0
            }
        
        # 计算决策区内的注视时间
        zone_fixation = self.calculate_fixation_duration(zone_df, file_info)
        
        # 计算决策区内的扫视指标
        zone_scan = self.calculate_scan_metrics(zone_df)
        
        return {
            'fixation_duration': zone_fixation['total_time'],
            'evacuation_sign': zone_fixation['evacuation_sign'],
            'fire': zone_fixation['fire'],
            'npc': zone_fixation['npc'],
            'scan_speed': zone_scan['speed'],
            'scan_distance': zone_scan['distance_total'],
            'saccade_count': zone_scan['saccade_count'],
            'mean_peak_velocity': zone_scan['mean_peak_velocity'],
            'median_amplitude': zone_scan['median_amplitude']
        }

    def calculate_group_zone_statistics(self, group_results, smoke_level, npc_count):
        """计算分组的决策区统计"""
        print(f"计算烟气等级{smoke_level}+NPC{npc_count}的决策区统计...")
        
        # 获取烟气等级名称
        smoke_name = {1: 'Low', 2: 'Medium', 3: 'High'}[smoke_level]
        group_name = f'Smoke_Level:{smoke_name} & NPC_Number:{npc_count}'
        
        # 初始化统计结果
        zone_stats = {
            'group': group_name,
            'smoke_level': smoke_level,
            'npc_count': npc_count
        }
        
        # 对每个决策区计算平均值
        for zone_name in ['zone1', 'zone2', 'zone3', 'zone4']:
            fixation_durations = []
            evacuation_sign = []
            fire =[]
            npc = []
            scan_speeds = []
            scan_distances = []
            saccade_counts = []
            peak_velocities = []
            amplitudes = []
            
            for result in group_results:
                if 'zone_metrics' in result and zone_name in result['zone_metrics']:
                    zone_metrics = result['zone_metrics'][zone_name]
                    fixation_durations.append(zone_metrics['fixation_duration'])
                    evacuation_sign.append(zone_metrics['evacuation_sign'])
                    fire.append(zone_metrics['fire'])
                    npc.append(zone_metrics['npc'])
                    scan_speeds.append(zone_metrics['scan_speed'])
                    scan_distances.append(zone_metrics['scan_distance'])
                    saccade_counts.append(zone_metrics['saccade_count'])
                    peak_velocities.append(zone_metrics['mean_peak_velocity'])
                    amplitudes.append(zone_metrics['median_amplitude'])
            
            # 计算平均值
            if fixation_durations:
                zone_stats[f'{zone_name}_fixation_duration'] = round(np.sum(fixation_durations), 3)
                zone_stats[f'{zone_name}_evacuation_sign'] = round(np.sum(evacuation_sign), 3)
                zone_stats[f'{zone_name}_fire'] = round(np.sum(fire), 3)
                zone_stats[f'{zone_name}_npc'] = round(np.sum(npc), 3)
                zone_stats[f'{zone_name}_scan_speed'] = round(np.mean(scan_speeds), 3)
                zone_stats[f'{zone_name}_scan_distance'] = round(np.mean(scan_distances), 3)
                zone_stats[f'{zone_name}_saccade_count'] = round(np.sum(saccade_counts), 3)
                zone_stats[f'{zone_name}_peak_velocity'] = round(np.mean(peak_velocities), 3)
                zone_stats[f'{zone_name}_amplitude'] = round(np.mean(amplitudes), 3)
            else:
                zone_stats[f'{zone_name}_fixation_duration'] = 0.0
                zone_stats[f'{zone_name}_evacuation_sign'] = 0.0
                zone_stats[f'{zone_name}_fire'] = 0.0
                zone_stats[f'{zone_name}_npc'] = 0.0
                zone_stats[f'{zone_name}_scan_speed'] = 0.0
                zone_stats[f'{zone_name}_scan_distance'] = 0.0
                zone_stats[f'{zone_name}_saccade_count'] = 0.0
                zone_stats[f'{zone_name}_peak_velocity'] = 0.0
                zone_stats[f'{zone_name}_amplitude'] = 0.0
        
        return zone_stats

    def save_results(self):
        """保存结果到CSV文件"""
        print("保存分析结果...")
        
        # 保存个体实验结果
        if self.individual_results:
            df_individual = pd.DataFrame(self.individual_results)
            
            # 展开嵌套的字典
            df_individual['fixation_duration_total'] = df_individual['fixation_duration'].apply(lambda x: x['total_time'])
            df_individual['fixation_duration_evacuation'] = df_individual['fixation_duration'].apply(lambda x: x['evacuation_sign'])
            df_individual['fixation_duration_fire'] = df_individual['fixation_duration'].apply(lambda x: x['fire'])
            df_individual['fixation_duration_npc'] = df_individual['fixation_duration'].apply(lambda x: x['npc'])
            df_individual['fixation_duration_other'] = df_individual['fixation_duration'].apply(lambda x: x['other'])
            
            # 删除嵌套字典列
            df_individual = df_individual.drop(['fixation_duration', 'zone_metrics'], axis=1)
            
            # 保存个体结果
            individual_file = self.data_root / "EyeIndicatorData_Individual.csv"
            df_individual.to_csv(individual_file, index=False, encoding='utf-8-sig')
            print(f"个体实验结果已保存: {individual_file}")
        
        # 保存分组+决策区结果
        if self.group_zone_results:
            df_group = pd.DataFrame(self.group_zone_results)
            group_file = self.data_root / "EyeIndicatorData_GroupZone.csv"
            df_group.to_csv(group_file, index=False, encoding='utf-8-sig')
            print(f"分组+决策区结果已保存: {group_file}")
        
        print("结果保存完成")

def get_grand_parent_folder():
    import os
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder

def get_target_file_path():
    import os
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = EyeIndicatorAssessment(get_target_file_path())
    
    # 分析眼动指标
    analyzer.analyze_all_eye_indicators()
