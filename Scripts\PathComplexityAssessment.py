import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import math
from scipy.spatial.distance import euclidean

class PathComplexityAssessment:
    """疏散路径复杂度评估器"""

    def __init__(self, data_root):
        self.data_root = Path(data_root)
        self.output_dir = self.data_root / "analysis_results" / "behavioral" / "path_complexity"
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        print(f"路径复杂度评估文件路径为：{self.output_dir}")
        
        # 镜像参考点
        self.mirror_point = (-92.5, -0.4318, -0.4991)
        
        # 出口坐标
        self.left_exit = np.array([51.3, -2.424, -2.872])
        self.right_exit = np.array([-228.7, -2.424, -2.769])

        # 理想路径长度
        self.ideal_Path_Length = 190 # m
        
        # 转向阈值
        self.turn_threshold = 60.0  # 度

        # 转弯检测方法选择
        # 'window': 基于滑动窗口的方法（推荐）
        # 'cumulative': 基于累积角度变化的方法
        self.turn_detection_method = 'cumulative'

    def analyze_path_complexity(self):
        """路劲复杂度分析"""

        print("###开始分析疏散路径复杂度###")

        # 读取行为数据文件
        behavioral_file = self.data_root / "BehavioralData1.csv"

        if not behavioral_file:
            print("未找到行为数据文件BehavioralData.csv")
            return

        df = pd.read_csv(behavioral_file, encoding='utf-8-sig', engine='python')

        # 筛选第二部分实验数据
        df_exp2 = df[df['experiment'] == 2].copy()

        if len(df_exp2) == 0:
            print("未找到第二次行为数据")
            return
        
        print(f"找到{len(df_exp2)}条第二次行为数据")

        # 存储结果
        results = []

        # 全量分组
        smoke_npc_combinations = [
            (1, 0), (1, 10), (1, 20),   # 低浓度
            (2, 0), (2, 10), (2, 20),   # 中浓度  
            (3, 0), (3, 10), (3, 20)    # 高浓度
        ]

        for smoke_level, npc_count in smoke_npc_combinations:
            # 筛选特定烟气浓度和NPC数量的数据
            condition_data = df_exp2[(df_exp2['smoke_level'] == smoke_level) & 
                (df_exp2['npc_count'] == npc_count)
            ].copy()

            if len(condition_data) == 0:
                print(f"烟气等级{smoke_level}+NPC{npc_count}的数据为空，跳过")
                continue

            print(f"开始分析烟气等级{smoke_level}+NPC{npc_count}的数据")

            # 计算改组的复杂度指标
            group_metrics = self.calculate_group_metrics(condition_data, smoke_level, npc_count)

            if group_metrics:
                results.append(group_metrics)
        
        # 保存结果
        if results:
            self.save_results(results)
        else:
            print("没有有效的分析结果")
            
        print("###路径复杂度分析完成###")

    def calculate_group_metrics(self, condition_data, smoke_level, npc_count):
        """计算单组的路径复杂度指标"""

        path_efficiencies = []
        turn_counts = []
        fractal_dimensions = []

        for _, row in condition_data.iterrows():
            # 加载并处理轨迹数据
            trajectory = self.load_trajectory_data(row)

            if trajectory is None:
                print("加载轨迹数据失败，跳过")
                continue
            
            # 1.计算路径效率
            path_efficiency = self.calculate_path_efficiency(trajectory, row)
            if path_efficiency is not None:
                path_efficiencies.append(path_efficiency)
            
            # 2.计算转弯次数
            if self.turn_detection_method == 'cumulative':
                turn_count = self.calculate_turn_count_alternative(trajectory)
            else:
                turn_count = self.calculate_turn_count(trajectory)
            turn_counts.append(turn_count)
            
            # 3.计算分形维数
            fractal_dim = self.calculate_fractal_dimension(trajectory)
            if fractal_dim is not None:
                fractal_dimensions.append(fractal_dim)
            else:
                fractal_dimensions.append(0)

        # 计算平均值
        if path_efficiencies and turn_counts and fractal_dimensions:
            # 获取烟气等级
            smoke_name = {1: 'Low', 2: 'Medium', 3: 'High'}[smoke_level]

            return{
                '分组': f'Smoke_Level:{smoke_name} & NPC_Number:{npc_count}',
                '平均路径效率': round(np.mean(path_efficiencies), 4),
                '平均转弯次数': round(np.mean(turn_counts), 2),
                '平均分形维数': round(np.mean(fractal_dimensions), 4)
            }
        
        return None
    
    def load_trajectory_data(self, row):
        """加载轨迹数据"""
        file_path = Path(row['file_path'])
        organized_file_path = file_path.parent / f"{file_path.stem}.csv" 

        if not organized_file_path.exists():
            print(f"文件{organized_file_path.name}不存在")
            return None
        
        try:
            df = pd.read_csv(organized_file_path, encoding='utf-8-sig', engine='python')
            
            # 检查必要列
            required_cols = ['PosX', 'PosZ']
            missing_cols = [col for col in required_cols if col not in df.columns]
            if missing_cols:
                print(f"缺少必要列: {missing_cols}")
                return None
            
            pos_x = df['PosX'].values
            pos_z = df['PosZ'].values
            
            # 处理镜像条件
            if row['is_mirror']:
                pos_x = 2 * self.mirror_point[0] - pos_x
            
            return {
                'x': pos_x,
                'z': pos_z
            }
        except Exception as e:
            print(f"处理轨迹数据时出错: {e}")
            return None
        
    def calculate_path_efficiency(self, trajectory, row):
        """计算路径效率 = 理想路径长度 / 实际路径长度"""

        try:
            # 若最终选择方向为左侧出口，则记录其效率为0，因为选择了不安全出口
            if row['evacuation_direction'] == 'left':
                return 0
            # 实际路径长度
            actual_distance = row['evacuation_distance']
            # 理想路径长度
            ideal_distance = self.ideal_Path_Length
            # 计算路径效率
            return ideal_distance / actual_distance
        except Exception as e:
            print(f"计算路径效率时出错: {e}")
        
        return None
    
    def calculate_turn_count(self, trajectory):
        """计算转弯次数 - 优化版本

        使用滑动窗口和多种策略来更准确地检测转弯：
        1. 基于时间窗口的方向变化检测
        2. 最小移动距离过滤
        3. 转弯间隔限制
        """
        try:
            x_coords = trajectory['x']
            z_coords = trajectory['z']

            if len(x_coords) < 10:  # 至少需要10个点
                return 0

            # 参数设置（基于45Hz采样率）
            window_size = 15  # 约0.33秒的窗口
            min_distance = 0.5  # 最小移动距离（米）
            min_turn_interval = 22  # 两次转弯之间的最小间隔（约0.5秒）

            return self._detect_turns_with_window(
                x_coords, z_coords, window_size, min_distance, min_turn_interval
            )

        except Exception as e:
            print(f"计算转弯次数时出错: {e}")
            return 0

    def _detect_turns_with_window(self, x_coords, z_coords, window_size, min_distance, min_turn_interval):
        """使用滑动窗口检测转弯"""
        turn_count = 0
        last_turn_index = -min_turn_interval

        for i in range(window_size, len(x_coords) - window_size):
            # 检查是否满足转弯间隔要求
            if i - last_turn_index < min_turn_interval:
                continue

            # 计算前后窗口的方向向量
            start_idx = i - window_size
            end_idx = i + window_size

            # 前半段方向
            front_vector = self._calculate_direction_vector(
                x_coords[start_idx:i], z_coords[start_idx:i], min_distance
            )

            # 后半段方向
            back_vector = self._calculate_direction_vector(
                x_coords[i:end_idx], z_coords[i:end_idx], min_distance
            )

            if front_vector is not None and back_vector is not None:
                # 计算角度变化
                angle_change = self._calculate_angle_between_vectors(front_vector, back_vector)

                if angle_change > self.turn_threshold:
                    turn_count += 1
                    last_turn_index = i

        return turn_count

    def _calculate_direction_vector(self, x_coords, z_coords, min_distance):
        """计算一段轨迹的主要方向向量"""
        if len(x_coords) < 2:
            return None

        # 使用起点和终点计算方向，但要确保有足够的移动距离
        start_point = np.array([x_coords[0], z_coords[0]])
        end_point = np.array([x_coords[-1], z_coords[-1]])

        direction_vector = end_point - start_point
        distance = np.linalg.norm(direction_vector)

        if distance < min_distance:
            return None

        return direction_vector / distance  # 归一化

    def _calculate_angle_between_vectors(self, v1, v2):
        """计算两个向量之间的角度（度）"""
        cos_angle = np.dot(v1, v2)
        cos_angle = np.clip(cos_angle, -1, 1)
        angle_rad = np.arccos(cos_angle)
        return np.degrees(angle_rad)

    def calculate_turn_count_alternative(self, trajectory):
        """备选方案：基于累积角度变化的转弯检测

        这种方法通过累积小的角度变化来检测显著的方向改变
        """
        try:
            x_coords = trajectory['x']
            z_coords = trajectory['z']

            if len(x_coords) < 20:
                return 0

            # 参数设置
            step_size = 45  # 每15个点计算一次方向
            cumulative_threshold = 70.0  # 累积角度阈值
            reset_threshold = 10.0  # 重置累积的小角度阈值
            min_distance = 0.3  # 最小移动距离

            turn_count = 0
            cumulative_angle = 0.0
            last_direction = None

            for i in range(0, len(x_coords) - step_size, step_size):
                # 计算当前段的方向向量
                start_point = np.array([x_coords[i], z_coords[i]])
                end_point = np.array([x_coords[i + step_size], z_coords[i + step_size]])

                direction_vector = end_point - start_point
                distance = np.linalg.norm(direction_vector)

                if distance < min_distance:
                    continue

                current_direction = direction_vector / distance

                if last_direction is not None:
                    # 计算角度变化
                    angle_change = self._calculate_angle_between_vectors(last_direction, current_direction)

                    if angle_change < reset_threshold:
                        # 小角度变化，重置累积
                        cumulative_angle = 0.0
                    else:
                        # 累积角度变化
                        cumulative_angle += angle_change

                        # 检查是否达到转弯阈值
                        if cumulative_angle >= cumulative_threshold:
                            turn_count += 1
                            cumulative_angle = 0.0  # 重置累积

                last_direction = current_direction

            return turn_count

        except Exception as e:
            print(f"计算转弯次数（备选方案）时出错: {e}")
            return 0
        
    def calculate_fractal_dimension(self, trajectory):
        """计算分形维数 - 优化版本

        使用多种方法来更准确地计算轨迹的分形维数：
        1. 数据预处理：去除冗余点，保留关键转折点
        2. 改进的盒计数法：使用更广泛的尺度范围
        3. 路径长度法：作为补充验证
        """
        try:
            x_coords = trajectory['x']
            z_coords = trajectory['z']

            if len(x_coords) < 10:
                return None

            # 方法1：改进的盒计数法
            fractal_dim_box = self._calculate_fractal_box_counting_improved(x_coords, z_coords)

            # 方法2：路径长度法（Divider method）
            fractal_dim_divider = self._calculate_fractal_divider_method(x_coords, z_coords)

            # 如果两种方法都有效，取平均值；否则返回有效的那个
            if fractal_dim_box is not None and fractal_dim_divider is not None:
                return (fractal_dim_box + fractal_dim_divider) / 2
            elif fractal_dim_box is not None:
                return fractal_dim_box
            elif fractal_dim_divider is not None:
                return fractal_dim_divider
            else:
                return None

        except Exception as e:
            print(f"计算分形维数时出错: {e}")
            return None

    def _calculate_fractal_box_counting_improved(self, x_coords, z_coords):
        """改进的盒计数法"""
        try:
            # 1. 数据预处理：去除冗余点
            simplified_points = self._simplify_trajectory(x_coords, z_coords)

            if len(simplified_points) < 4:
                return None

            # 计算边界框
            min_x, min_z = np.min(simplified_points, axis=0)
            max_x, max_z = np.max(simplified_points, axis=0)

            # 计算轨迹的实际范围
            range_x = max_x - min_x
            range_z = max_z - min_z
            max_range = max(range_x, range_z)

            if max_range <= 0.1:  # 如果轨迹范围太小
                return None

            box_sizes = []
            box_counts = []

            # 使用更广泛的尺度范围，从轨迹总长度的1/4到1/128
            for i in range(2, 10):  # 8个不同的尺度
                box_size = max_range / (2 ** i)

                if box_size < 0.05:  # 最小盒子大小限制
                    break

                # 计算被轨迹穿过的盒子数量
                count = self._count_boxes_intersected_by_path(
                    simplified_points, min_x, min_z, box_size
                )

                if count > 0:
                    box_sizes.append(box_size)
                    box_counts.append(count)

            if len(box_sizes) < 4:  # 至少需要4个数据点
                return None

            # 使用线性回归计算分形维数
            # 检查数据是否为正数
            if np.any(np.array(box_sizes) <= 0) or np.any(np.array(box_counts) <= 0):
                return None
                
            log_sizes = np.log(box_sizes)
            log_counts = np.log(box_counts)

            # 检查数据有效性
            if len(log_sizes) < 2 or len(log_counts) < 2:
                return None
            
            # 检查是否有无效值
            if np.any(np.isnan(log_sizes)) or np.any(np.isnan(log_counts)) or \
               np.any(np.isinf(log_sizes)) or np.any(np.isinf(log_counts)):
                return None
            
            # 检查标准差是否为零
            if np.std(log_sizes) == 0 or np.std(log_counts) == 0:
                return None

            # 计算相关系数，确保拟合质量
            try:
                correlation = np.corrcoef(log_sizes, log_counts)[0, 1]
                if np.isnan(correlation):
                    return None
            except Exception:
                return None

            if abs(correlation) < 0.8:  # 相关性太低
                return None

            # 线性拟合
            try:
                coeffs = np.polyfit(log_sizes, log_counts, 1)
                fractal_dimension = -coeffs[0]
                
                # 检查拟合结果是否有效
                if np.isnan(fractal_dimension) or np.isinf(fractal_dimension):
                    return None
                
                # 分形维数应该在1到2之间（对于2D轨迹）
                if 1.0 <= fractal_dimension <= 2.0:
                    return fractal_dimension
                else:
                    return None
            except Exception:
                print(f"盒计数法线性拟合出错:{e}")
                return None

        except Exception as e:
            print(f"改进盒计数法计算出错: {e}")
            return None

    def _simplify_trajectory(self, x_coords, z_coords, min_distance=0.5):
        """简化轨迹，去除冗余点"""
        points = []
        last_point = None

        for i in range(len(x_coords)):
            current_point = np.array([x_coords[i], z_coords[i]])

            if last_point is None:
                points.append(current_point)
                last_point = current_point
            else:
                # 只保留距离上一个保留点足够远的点
                distance = np.linalg.norm(current_point - last_point)
                if distance >= min_distance:
                    points.append(current_point)
                    last_point = current_point

        # 确保包含最后一个点
        if len(points) > 0:
            final_point = np.array([x_coords[-1], z_coords[-1]])
            if np.linalg.norm(final_point - points[-1]) > 0.1:
                points.append(final_point)

        return np.array(points) if points else np.array([[0, 0]])

    def _count_boxes_intersected_by_path(self, points, min_x, min_z, box_size):
        """计算被路径穿过的盒子数量"""
        occupied_boxes = set()

        # 对于每条线段，找出它穿过的所有盒子
        for i in range(len(points) - 1):
            start_point = points[i]
            end_point = points[i + 1]

            # 使用Bresenham算法的思想，找出线段穿过的所有网格
            boxes_on_segment = self._get_boxes_on_line_segment(
                start_point, end_point, min_x, min_z, box_size
            )
            occupied_boxes.update(boxes_on_segment)

        return len(occupied_boxes)

    def _get_boxes_on_line_segment(self, start, end, min_x, min_z, box_size):
        """获取线段穿过的所有盒子"""
        boxes = set()

        # 将起点和终点转换为网格坐标
        start_grid = ((start[0] - min_x) / box_size, (start[1] - min_z) / box_size)
        end_grid = ((end[0] - min_x) / box_size, (end[1] - min_z) / box_size)

        # 在线段上采样点
        num_samples = max(10, int(np.linalg.norm(np.array(end_grid) - np.array(start_grid)) * 2))

        for i in range(num_samples + 1):
            t = i / num_samples
            current_grid = (
                start_grid[0] + t * (end_grid[0] - start_grid[0]),
                start_grid[1] + t * (end_grid[1] - start_grid[1])
            )

            box_x = int(current_grid[0])
            box_z = int(current_grid[1])
            boxes.add((box_x, box_z))

        return boxes

    def _calculate_fractal_divider_method(self, x_coords, z_coords):
        """路径长度法（Divider method）计算分形维数"""
        try:
            # 简化轨迹
            simplified_points = self._simplify_trajectory(x_coords, z_coords, min_distance=0.3)

            if len(simplified_points) < 4:
                return None

            # 计算轨迹的总长度范围
            total_length = self._calculate_path_length(simplified_points)

            if total_length < 1.0:  # 轨迹太短
                return None

            # 不同的测量尺度
            scales = []
            lengths = []

            # 从轨迹总长度的1/4开始，逐步减小尺度
            base_scale = total_length / 4

            for i in range(6):  # 6个不同的尺度
                scale = base_scale / (2 ** i)

                if scale < 0.2:  # 最小尺度限制
                    break

                # 使用当前尺度测量路径长度
                measured_length = self._measure_path_with_scale(simplified_points, scale)

                if measured_length > 0:
                    scales.append(scale)
                    lengths.append(measured_length)

            if len(scales) < 4:
                return None

            # 计算分形维数：log(L) vs log(1/scale)
            # 检查数据是否为正数
            if np.any(np.array(scales) <= 0) or np.any(np.array(lengths) <= 0):
                return None
                
            log_scales = np.log([1/s for s in scales])
            log_lengths = np.log(lengths)

            # 检查数据有效性
            if len(log_scales) < 2 or len(log_lengths) < 2:
                return None
            
            # 检查是否有无效值
            if np.any(np.isnan(log_scales)) or np.any(np.isnan(log_lengths)) or \
               np.any(np.isinf(log_scales)) or np.any(np.isinf(log_lengths)):
                return None
            
            # 检查标准差是否为零
            if np.std(log_scales) == 0 or np.std(log_lengths) == 0:
                return None

            # 检查相关性
            try:
                correlation = np.corrcoef(log_scales, log_lengths)[0, 1]
                if np.isnan(correlation):
                    return None
            except Exception:
                return None

            if abs(correlation) < 0.7:
                return None

            # 线性拟合
            try:
                coeffs = np.polyfit(log_scales, log_lengths, 1)
                fractal_dimension = coeffs[0]
                
                # 检查拟合结果是否有效
                if np.isnan(fractal_dimension) or np.isinf(fractal_dimension):
                    return None
                
                # 分形维数应该在1到2之间
                if 1.0 <= fractal_dimension <= 2.0:
                    return fractal_dimension
                else:
                    return None
            except Exception:
                print(f"路径长度法线性拟合出错:{e}")
                return None

        except Exception as e:
            print(f"路径长度法计算出错: {e}")
            return None

    def _calculate_path_length(self, points):
        """计算路径总长度"""
        total_length = 0
        for i in range(len(points) - 1):
            segment_length = np.linalg.norm(points[i + 1] - points[i])
            total_length += segment_length
        return total_length

    def _measure_path_with_scale(self, points, scale):
        """使用指定尺度测量路径长度"""
        if len(points) < 2:
            return 0

        measured_length = 0
        current_pos = points[0].copy()

        for i in range(1, len(points)):
            target = points[i]

            while True:
                # 计算当前位置到目标点的距离
                remaining_distance = np.linalg.norm(target - current_pos)

                if remaining_distance <= scale:
                    # 如果剩余距离小于等于尺度，直接到达目标点
                    measured_length += remaining_distance
                    current_pos = target.copy()
                    break
                else:
                    # 沿着方向移动一个尺度的距离
                    direction = (target - current_pos) / remaining_distance
                    current_pos += direction * scale
                    measured_length += scale

        return measured_length
        
    def save_results(self, results):
        """保存结果到CSV文件"""
        df_results = pd.DataFrame(results)
        
        # 保存到CSV文件
        output_file = self.data_root / "PathComplexity.csv"
        df_results.to_csv(output_file, index=False, encoding='utf-8-sig')
        
        print(f"路径复杂度分析结果已保存: {output_file}")
        print("\n结果预览:")
        print(df_results.to_string(index=False))

def get_grand_parent_folder():
    import os
    current_script_path = os.path.abspath(__file__)
    current_folder = os.path.dirname(current_script_path)
    parent_folder = os.path.dirname(current_folder)
    return parent_folder

def get_target_file_path():
    import os
    return get_grand_parent_folder() + os.sep + "ScriptsTest"

if __name__ == "__main__":
    # 创建分析器实例
    analyzer = PathComplexityAssessment(get_target_file_path())
    
    # 分析路径复杂度
    analyzer.analyze_path_complexity()